<!-- 顶部标题 -->
<template>
  <header class="header">
    <!-- 左侧导航按钮 -->
    <div class="nav-buttons">
      <div class="nav-button">
        <img src="@/assets/images/bg_nav_box.png" alt="导航背景" />
        <span class="button-text">市政设施一张图</span>
      </div>
      <div class="nav-button">
        <img src="@/assets/images/bg_nav_box.png" alt="导航背景" />
        <span class="button-text">桥隧总览</span>
      </div>
    </div>
    
    <!-- 中央标题 -->
    <div class="title">市政设施运维管理平台</div>
    
    <!-- 右侧日期天气 -->
    <div class="weather-info">
      <div class="time">11:33:05</div>
      <div class="date">
        <div>2025-7-30</div>
        <div>星期三</div>
      </div>
      <div class="weather">
        <div class="sun-icon">☀</div>
        <div class="weather-details">
          <div class="weather-text">晴</div>
          <div class="temperature">27.9C°</div>
        </div>
      </div>
    </div>
  </header>
  
  <!-- 市政设施统计卡片 -->
  <div class="facility-stats">
    <div class="stat-card">
      <div class="stat-header">
        <div class="stat-label">车行桥梁</div>
        <div class="stat-icon">
          <img src="@/assets/images/ic_car.png" alt="车行桥梁" />
        </div>
      </div>
      <div class="stat-value">45<span class="stat-unit">个</span></div>
    </div>
    <div class="stat-card">
      <div class="stat-header">
        <div class="stat-label">人行天桥</div>
        <div class="stat-icon">
          <img src="@/assets/images/ic_blind.png" alt="人行天桥" />
        </div>
      </div>
      <div class="stat-value">13<span class="stat-unit">个</span></div>
    </div>
    <div class="stat-card">
      <div class="stat-header">
        <div class="stat-label">地下通道</div>
        <div class="stat-icon">
          <img src="@/assets/images/ic_under.png" alt="地下通道" />
        </div>
      </div>
      <div class="stat-value">0<span class="stat-unit">个</span></div>
    </div>
    <div class="stat-card">
      <div class="stat-header">
        <div class="stat-label">隧道</div>
        <div class="stat-icon">
          <img src="@/assets/images/ic_tunnel.png" alt="隧道" />
        </div>
      </div>
      <div class="stat-value">9<span class="stat-unit">个</span></div>
    </div>
  </div>
</template>

<script setup>
// 市政设施统计数据
const facilityStats = [
  {
    label: '车行桥梁',
    value: 45,
    unit: '个',
    icon: 'ic_car.png'
  },
  {
    label: '人行天桥',
    value: 13,
    unit: '个',
    icon: 'ic_blind.png'
  },
  {
    label: '地下通道',
    value: 0,
    unit: '个',
    icon: 'ic_under.png'
  },
  {
    label: '隧道',
    value: 9,
    unit: '个',
    icon: 'ic_tunnel.png'
  }
]
</script>

<style lang="scss" scoped>
.header {
  position: absolute;
  margin: 0 12px;
  top: 12px;
  width: calc(100% - 24px);
  height: 87px;
  background: url('@/assets/images/bg_top.png') no-repeat center center;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-sizing: border-box;
}

.nav-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 1rem;
}

.nav-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  img {
    width: 120px;
    height: 40px;
    object-fit: cover;
  }

  .button-text {
    position: absolute;
    color: rgba(230, 239, 253);
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
  }
}

.title {
  font-size: 40px;
  font-weight: 800;
  letter-spacing: 12px;
  color: rgba(230, 239, 253);
  text-align: center;
  flex: 1;
}

.weather-info {
  display: flex;
  // flex-direction: column;
  align-items: flex-end;
  gap: 5px;
  color: rgba(230, 239, 253);
  font-size: 14px;
  font-weight: 500;
 margin-bottom: 1rem;
  .time {
    font-size: 16px;
    font-weight: bold;
  }

  .date {
    text-align: right;
    line-height: 1.2;
  }

  .weather {
    display: flex;
    align-items: center;
    gap: 5px;

    .sun-icon {
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      color: #FFD700;
    }

    .weather-details {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      line-height: 1.2;

      .weather-text {
        font-size: 12px;
      }

      .temperature {
        font-size: 12px;
      }
    }
  }
}

.facility-stats {
  position: absolute;
  top: 120px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: center;
}

.stat-card {
  width: 180px;
  height: 80px;
  background: url('@/assets/images/bg_center_top_box.png') no-repeat center center;
  background-size: 100% 100%;
  padding: 15px 20px;
  box-sizing: border-box;
  position: relative;
  display: flex;
  flex-direction: column;

  .stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    margin-left: 1rem;

    .stat-label {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.8);
      line-height: 1;
    }

    .stat-icon {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }

  .stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #00FFFF;
    line-height: 1;
    text-align: center;

    .stat-unit {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      margin-left: 2px;
    }
  }
}
</style>
