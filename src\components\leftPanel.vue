左侧数据面板
<template>
  <div class="left-panel">
   <!-- 今日巡检情况 -->
    <left2 />
    <!-- 检测告警 -->
    <left1 />

    <!-- 视频监控 -->
    <left3 />
  </div>
</template>

<script setup>
import left1 from './leftPanel/left1.vue'
import left2 from './leftPanel/left2.vue'
import left3 from './leftPanel/left3.vue'
</script>

<style lang="scss" scoped>
.left-panel {
  position: absolute;
  width: 500px;
  height: 100%;
  left: 0;
  top: 0;
  display: grid;
  gap: 24px;
  padding: 111px 0 34px 16px;
  box-sizing: border-box;
  grid-template-columns: 1fr;
  grid-template-rows: repeat(3, 1fr);

  // 确保每个子组件都有相同的最小高度
  > * {
    min-height: 280px;
    height: 100%;
  }
}
</style>
